import { Analytics, logEvent } from 'firebase/analytics';
import { getAnalytics } from 'firebase/app';
import { initializeApp } from 'firebase/app';

const firebaseConfig = {
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
    appId: "YOUR_APP_ID",
    measurementId: "YOUR_MEASUREMENT_ID"
};

const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

const AnalyticsService = {
    logScreenView: (screenName) => {
        logEvent(analytics, 'screen_view', {
            screen_name: screenName,
        });
    },
    logUserEngagement: () => {
        logEvent(analytics, 'user_engagement');
    },
    logCustomEvent: (eventName, eventParams) => {
        logEvent(analytics, eventName, eventParams);
    },
};

export default AnalyticsService;