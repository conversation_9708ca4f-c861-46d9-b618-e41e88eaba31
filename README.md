# Tahqeeq AI

A comprehensive and advanced mobile application for Islamic research, endorsed by the Sawade Azam organization. Tahqeeq AI provides authentic answers on Islamic fiqh, tafsir, hadith, Arabic grammar, tasawwuf, and other Islamic sciences.

## Overview

Tahqeeq AI is designed for scholars, students, and general users seeking authentic Islamic knowledge. The application features a professional, premium-looking interface and supports questions and answers in multiple languages including Urdu, Arabic, Hindi, English, and Roman Urdu.

### Key Features

- **AI-Powered Research**: Utilizes Hugging Face Transformers for understanding queries, retrieving answers, and formatting responses
- **Dual Chat Modes**: Scholars Option for detailed answers and Students Option for simplified explanations
- **Comprehensive Islamic Libraries**: Integration with multiple free APIs and libraries for authentic Islamic content
- **PDF Upload**: Upload and search through personal Islamic texts
- **Multilingual Support**: Full support for Urdu, Arabic, Hindi, English, and Roman Urdu
- **Offline Access**: Core features available without internet connection
- **Professional UI**: Premium design with proper Islamic aesthetics and fonts

## Technical Details

### Framework
- React Native Expo for cross-platform compatibility (Android 8.0 and above, iOS 12.0 and above)

### Database
- SQLite for local storage
- Firebase Firestore for cloud syncing

### AI Integration
- Hugging Face Transformers for natural language processing
- OCR (Tesseract) for PDF text extraction

### API Integrations
- Hadith API (hadithapi.com)
- Quran-api (github.com/fawazahmed0/quran-api)
- Al Quran Cloud (alquran.cloud)
- Islamhouse API (islamhouse.com)
- Open Library APIs (openlibrary.org)
- Web scraping from various Islamic repositories

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development)

### Installation
1. Clone the repository
   ```
   git clone https://github.com/sawadeazam/tahqeeq-ai.git
   cd tahqeeq-ai
   ```

2. Install dependencies
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

3. Start the development server
   ```
   expo start
   ```

### Building for Production
- For Android:
  ```
  expo build:android
  ```

- For iOS:
  ```
  expo build:ios
  ```

## Project Structure

```
tahqeeq-ai/
├── app/                  # Application code
│   ├── components/       # Reusable UI components
│   ├── screens/          # Application screens
│   ├── navigation/       # Navigation configuration
│   ├── services/         # API and service integrations
│   │   ├── firebase.ts
│   │   ├── firestore.ts
│   │   ├── analytics.ts
│   │   └── sqlite.ts
│   ├── utils/            # Utility functions
│   └── hooks/            # Custom React hooks
├── assets/               # Static assets (images, fonts)
│   ├── fonts/            # Font files
│   └── icon/             # Application icon
│       └── app-icon.svg  # Black-and-white app icon
├── data/                 # Local data and schemas
├── docs/                 # Documentation
└── README.md             # Project documentation
```

## Free API Integration Details

### Hadith API
- Source: hadithapi.com
- Usage: Retrieves verified Hadith collections in Arabic, English, and Urdu
- Implementation: REST API calls with caching for offline access

### Quran API
- Source: github.com/fawazahmed0/quran-api
- Usage: Provides Quranic text, translations, and audio recitations
- Implementation: REST API calls with local storage for offline access

### Al Quran Cloud
- Source: alquran.cloud
- Usage: Provides Quranic text, translations, audio recitations, and Juz/Surah access
- Implementation: REST API calls with caching

### Islamhouse API
- Source: islamhouse.com
- Usage: Provides Islamic books, articles, audio, and videos
- Implementation: REST API calls with content filtering for Sunni Hanafi sources

### Open Library APIs
- Source: openlibrary.org
- Usage: Provides metadata and texts of Islamic books
- Implementation: REST API calls with focus on Sunni Hanafi works

## Web Scraping Implementation

The application ethically scrapes content from the following sources:
- tanzil.net for Quranic texts
- hadithcollection.com for Hadith collections
- quranurdu.com for Urdu translations

Implementation details:
- Uses axios and cheerio for HTML parsing
- Implements rate limiting to avoid server overload
- Caches scraped content locally for offline access
- Filters content to ensure only Sunni Hanafi sources are included

## AI Integration

The application uses Hugging Face Transformers, an open-source NLP library:
- Models: multilingual-bert-base for language understanding
- Implementation: TensorFlow.js for on-device inference
- Features:
  - Query understanding across multiple languages
  - Context-aware answer retrieval
  - Scholarly formatting of responses
  - Language detection and matching

## Usage Guidelines

1. **Chat Interface**: Use the chat interface to ask questions about Islamic topics
2. **Language Selection**: The app automatically detects the language of your query
3. **Chat Options**: Choose between Scholars Option (detailed) or Students Option (simplified)
4. **PDF Upload**: Upload Islamic texts for the AI to reference
5. **Offline Mode**: Core features work without internet connection
6. **Bookmarks**: Save important references for later access
7. **Share**: Generate and share beautiful PDFs of answers

## Sawade Azam Organization

Tahqeeq AI is endorsed by the Sawade Azam organization. Visit [www.sawadeazam.org](https://www.sawadeazam.org) for more information about their work in promoting authentic Islamic knowledge.

## License

This project is developed for the Sawade Azam organization and is not open for redistribution or modification without explicit permission.